package cn.tianxing.cloud.module.system.framework.rpc.config;

import cn.tianxing.cloud.module.crm.api.phone.VirtualPhoneApi;
import cn.tianxing.cloud.module.infra.api.config.ConfigApi;
import cn.tianxing.cloud.module.infra.api.file.FileApi;
import cn.tianxing.cloud.module.infra.api.websocket.WebSocketSenderApi;
import cn.tianxing.cloud.module.member.api.user.MemberUserApi;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Configuration;

@Configuration(proxyBeanMethods = false)
@EnableFeignClients(clients = {
        FileApi.class,
        WebSocketSenderApi.class,
        ConfigApi.class,
        MemberUserApi.class,
        VirtualPhoneApi.class
})
public class RpcConfiguration {
}
