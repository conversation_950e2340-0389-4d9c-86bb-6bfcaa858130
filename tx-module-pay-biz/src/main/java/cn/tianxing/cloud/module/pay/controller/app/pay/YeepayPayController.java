package cn.tianxing.cloud.module.pay.controller.app.pay;


import cn.tianxing.cloud.framework.common.pojo.CommonResult;
import cn.tianxing.cloud.module.pay.config.YeepayProperties;
import cn.tianxing.cloud.module.pay.controller.app.pay.vo.YeepayCreateOrderRespVO;
import com.yeepay.yop.sdk.exception.YopClientException;
import com.yeepay.yop.sdk.service.aggpay.AggpayClient;
import com.yeepay.yop.sdk.service.aggpay.AggpayClientBuilder;
import com.yeepay.yop.sdk.service.aggpay.request.PrePayRequest;
import com.yeepay.yop.sdk.service.aggpay.response.PrePayResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;

import static cn.tianxing.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "我的易宝支付")
@RestController
@RequestMapping("/pay/myyeepay")
@Validated
@Slf4j
public class YeepayPayController {


    // 该Client线程安全，请使用单例模式，多次请求共用
    private static final AggpayClient api = AggpayClientBuilder.builder().build();

    @Resource
    private YeepayProperties yeepayProperties;



    @PostMapping("/createOrder")
    @Operation(summary = "创建支付订单")
    @PermitAll
    public CommonResult<YeepayCreateOrderRespVO> createOrder() {
        PrePayRequest request = new PrePayRequest();
        
        // 使用配置类中的商户信息
        request.setParentMerchantNo(yeepayProperties.getParentMerchantNo());
        request.setMerchantNo(yeepayProperties.getMerchantNo());
        
        // 生成订单号（实际项目中应该使用业务逻辑生成）
        String orderId = "ORDER" + System.currentTimeMillis();
        request.setOrderId(orderId);
        
        // 设置订单金额（实际项目中应该从请求参数获取）
        request.setOrderAmount(new BigDecimal("0.01"));
        
        // 设置过期时间（使用配置的过期时间）
        request.setExpiredTime(new DateTime().plusMinutes(yeepayProperties.getOrderExpireMinutes()));
        
        // 使用配置类中的回调地址
//        request.setNotifyUrl(yeepayProperties.getNotifyUrl());
        request.setNotifyUrl("https://notify.merchant.com/xxx");
//        request.setRedirectUrl(yeepayProperties.getNotifyUrl()); // 重定向地址也使用回调地址
        request.setRedirectUrl("https://notify.merchant.com/xxx");
        
        // 设置商品信息（使用配置的默认值）
        request.setMemo(yeepayProperties.getDefaultMemo());
        request.setGoodsName(yeepayProperties.getDefaultGoodsName());
        
        // 设置支付参数（使用配置的默认值）
        request.setFundProcessType(yeepayProperties.getDefaultFundProcessType());
        request.setPayWay(yeepayProperties.getDefaultPayWay());
        request.setChannel(yeepayProperties.getDefaultChannel());
        request.setScene(yeepayProperties.getDefaultScene());
        
        request.setAppId("wx368b88ba5087bc8c");

        request.setUserId("oKH9a7DOUH-Oq1b1jH7g50WWcqmM");
        request.setUserIp("127.0.0.1");
        
        // 设置渠道特定信息
//        request.setChannelSpecifiedInfo("{\"hbFqNum\":\"3\",\"hbFqSellerPercent\":\"0\",\"sysServiceProviderId\":\"\"}");
//        request.setChannelPromotionInfo("channelPromotionInfo_example");
        
        // 设置身份信息（实际项目中应该从请求参数获取）
//        request.setIdentityInfo("{\"identityVerifyType\":\"N\",\"payerIdType\":\"IDENTITY_CARD\",\"payerNumber\":\"\",\"payerName\":\"\"}");
        
        // 设置其他参数
//        request.setLimitCredit("N");
//        request.setUniqueOrderNo(orderId); // 使用相同的订单号
//        request.setToken(""); // 实际项目中应该生成token
        
        // 设置回调相关
//        request.setCsUrl("csUrl_example");
//        request.setAccountLinkInfo("{accountProvider\":\"BOL\",\"token\":\"xxx\"}");
        
        // 设置促销信息
//        request.setYpPromotionInfo("自定义支付立减：[{\"amount\":\"0.01\",\"type\":\"CUSTOM_REDUCTION\"}],自定义补贴商户[{\"type\":\"CUSTOM_ALLOWANCE\"}]");
        
        // 设置银行信息（使用配置的默认银行代码）
//        request.setBankCode(yeepayProperties.getDefaultBankCode());
//        request.setBusinessInfo("businessInfo_example");
//        request.setUserAuthCode("userAuthCode_example");
        
        // 设置渠道活动信息
//        request.setChannelActivityInfo("{\"food_order_type\":\"QR_FOOD_ORDER\"}");
        
        // 设置终端信息
//        request.setTerminalId("terminalId_example");
//        request.setTerminalSceneInfo("{\"storeId\":\"门店id\",\"storeName\":\"门店名称\",\"operatorId\":\"商户操作员编号\",\"alipayStoreId\":\"支付宝的店铺编号\",\"areaCode\":\"门店行政区划码\",\"address\":\"门店详细地址\"}");
        
        // 设置其他业务参数
//        request.setYpAccountBookNo("ypAccountBookNo_example");
//        request.setTerminalInfo("{\"shopName\":\"网点名称\",\"shopCustomerNumber\":\"网点编号\"}");
//        request.setProductInfo("[{\"id\":\"random_reduction_pro\"}]");
//        request.setDivideDetail("[{\"amount\":\"金额\",\"ledgerNo\":\"分账商编\",\"divideDetailDesc\":\"分账说明\"}]");
//        request.setDivideNotifyUrl("divideNotifyUrl_example");
//        request.setFeeSubsidyInfo("[{\"subsidyMerchantNo\":\"***********\",\"subsidyAccountType\":\"FEE_ACCOUNT\",\"subsidyType\":\"ABSOLUTE\",\"subsidyProportion\":\"\",\"subsidyCalculateType\":\"SINGLE_PERCENT\",\"subsidyPercentFee\":\"0.6\",\"subsidyFixedFee\":\"\",\"subsidySingleMaxFee\":\"\"}]");
//        request.setAgreementId("agreementId_example");
//        request.setCreditOrderId("creditOrderId_example");
        
        try {
            // 打印请求参数（JSON格式）
            log.info("易宝支付预支付请求参数: {}", 
                new com.fasterxml.jackson.databind.ObjectMapper()
                    .registerModule(new com.fasterxml.jackson.datatype.joda.JodaModule())
                    .writeValueAsString(request));
        } catch (com.fasterxml.jackson.core.JsonProcessingException e) {
            log.error("打印易宝支付请求参数失败: {}", e.getMessage(), e);
        }
        try {

            PrePayResponse response = api.prePay(request);
            log.info("易宝支付预支付响应: {}", response);

            // 包装响应数据
            YeepayCreateOrderRespVO respVO = new YeepayCreateOrderRespVO();
            if (response.getResult() != null) {
                respVO.setCode(response.getResult().getCode());
                respVO.setMessage(response.getResult().getMessage());
                respVO.setOrderId(response.getResult().getOrderId());
                respVO.setUniqueOrderNo(response.getResult().getUniqueOrderNo());
                respVO.setBankOrderId(response.getResult().getBankOrderId());
                respVO.setPrePayTn(response.getResult().getPrePayTn());
            }
            respVO.setSuccess(response.isSuccess());
            respVO.setRawResponse(response);

            return success(respVO);
        } catch (YopClientException e) {
            log.error("易宝支付调用失败: {}", e.getMessage());

            // 创建失败响应
            YeepayCreateOrderRespVO errorRespVO = new YeepayCreateOrderRespVO();
            errorRespVO.setSuccess(false);
            errorRespVO.setMessage("易宝支付调用失败: " + e.getMessage());

            return success(errorRespVO);
        }
    }

}
