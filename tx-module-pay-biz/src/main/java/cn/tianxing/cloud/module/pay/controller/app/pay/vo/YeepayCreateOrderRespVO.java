package cn.tianxing.cloud.module.pay.controller.app.pay.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 易宝支付创建订单响应 VO
 *
 * <AUTHOR>
 */
@Schema(description = "易宝支付创建订单响应 VO")
@Data
public class YeepayCreateOrderRespVO {

    @Schema(description = "是否成功", requiredMode = Schema.RequiredMode.REQUIRED, example = "true")
    private Boolean success;

    @Schema(description = "响应码", example = "00000")
    private String code;

    @Schema(description = "响应消息", example = "成功")
    private String message;

    @Schema(description = "订单号", example = "ORDER1234567890")
    private String orderId;

    @Schema(description = "易宝唯一订单号", example = "YP123456789")
    private String uniqueOrderNo;

    @Schema(description = "银行订单号", example = "BANK123456789")
    private String bankOrderId;

    @Schema(description = "预支付标识信息", example = "prePayTn_example")
    private String prePayTn;

    @Schema(description = "原始响应数据", hidden = true)
    private Object rawResponse;

}
