/*
 * 会员钱包
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 1.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.yeepay.yop.sdk.service.m_wallet.request;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
public class TransferB2cMarketV10Request extends com.yeepay.yop.sdk.model.BaseRequest {
    private static final long serialVersionUID = 1L;

    private String parentMerchantNo;

    private String fromMerchantNo;

    private String toMerchantNo;

    private String toMerchantUserNo;

    private String requestNo;

    private String orderAmount;

    private String feeChargeSide;

    private String notifyUrl;

    private String remark;


    /**
     * Get parentMerchantNo
     * @return parentMerchantNo
     **/
    
    public String getParentMerchantNo() {
        return parentMerchantNo;
    }

    public void setParentMerchantNo(String parentMerchantNo) {
        this.parentMerchantNo = parentMerchantNo;
    }

    /**
     * Get fromMerchantNo
     * @return fromMerchantNo
     **/
    
    public String getFromMerchantNo() {
        return fromMerchantNo;
    }

    public void setFromMerchantNo(String fromMerchantNo) {
        this.fromMerchantNo = fromMerchantNo;
    }

    /**
     * Get toMerchantNo
     * @return toMerchantNo
     **/
    
    public String getToMerchantNo() {
        return toMerchantNo;
    }

    public void setToMerchantNo(String toMerchantNo) {
        this.toMerchantNo = toMerchantNo;
    }

    /**
     * Get toMerchantUserNo
     * @return toMerchantUserNo
     **/
    
    public String getToMerchantUserNo() {
        return toMerchantUserNo;
    }

    public void setToMerchantUserNo(String toMerchantUserNo) {
        this.toMerchantUserNo = toMerchantUserNo;
    }

    /**
     * Get requestNo
     * @return requestNo
     **/
    
    public String getRequestNo() {
        return requestNo;
    }

    public void setRequestNo(String requestNo) {
        this.requestNo = requestNo;
    }

    /**
     * Get orderAmount
     * @return orderAmount
     **/
    
    public String getOrderAmount() {
        return orderAmount;
    }

    public void setOrderAmount(String orderAmount) {
        this.orderAmount = orderAmount;
    }

    /**
     * Get feeChargeSide
     * @return feeChargeSide
     **/
    
    public String getFeeChargeSide() {
        return feeChargeSide;
    }

    public void setFeeChargeSide(String feeChargeSide) {
        this.feeChargeSide = feeChargeSide;
    }

    /**
     * Get notifyUrl
     * @return notifyUrl
     **/
    
    public String getNotifyUrl() {
        return notifyUrl;
    }

    public void setNotifyUrl(String notifyUrl) {
        this.notifyUrl = notifyUrl;
    }

    /**
     * Get remark
     * @return remark
     **/
    
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Override
    public String getOperationId() {
        return "transfer_b2c_market_v1_0";
    }
    private final Map<String, Object> _extParamMap = new HashMap<>();

    public void addParam(String name, Object value) {
        if (null != name && null != value) {
            validateParameter(name, value);
            _extParamMap.put(name, value);
        }
    }

    public Map<String, Object> get_extParamMap() {
        return Collections.unmodifiableMap(_extParamMap);
    }
}
