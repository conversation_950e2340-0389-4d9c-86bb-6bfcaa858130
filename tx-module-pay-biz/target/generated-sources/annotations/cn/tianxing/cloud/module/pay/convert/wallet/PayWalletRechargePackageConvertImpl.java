package cn.tianxing.cloud.module.pay.convert.wallet;

import cn.tianxing.cloud.framework.common.pojo.PageResult;
import cn.tianxing.cloud.module.pay.controller.admin.wallet.vo.rechargepackage.WalletRechargePackageCreateReqVO;
import cn.tianxing.cloud.module.pay.controller.admin.wallet.vo.rechargepackage.WalletRechargePackageRespVO;
import cn.tianxing.cloud.module.pay.controller.admin.wallet.vo.rechargepackage.WalletRechargePackageUpdateReqVO;
import cn.tianxing.cloud.module.pay.dal.dataobject.wallet.PayWalletRechargePackageDO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-18T16:56:03+0800",
    comments = "version: 1.6.3, compiler: Eclipse JDT (IDE) 3.42.50.v20250729-0351, environment: Java 21.0.8 (Eclipse Adoptium)"
)
public class PayWalletRechargePackageConvertImpl implements PayWalletRechargePackageConvert {

    @Override
    public PayWalletRechargePackageDO convert(WalletRechargePackageCreateReqVO bean) {
        if ( bean == null ) {
            return null;
        }

        PayWalletRechargePackageDO payWalletRechargePackageDO = new PayWalletRechargePackageDO();

        payWalletRechargePackageDO.setBonusPrice( bean.getBonusPrice() );
        payWalletRechargePackageDO.setName( bean.getName() );
        payWalletRechargePackageDO.setPayPrice( bean.getPayPrice() );
        if ( bean.getStatus() != null ) {
            payWalletRechargePackageDO.setStatus( bean.getStatus().intValue() );
        }

        return payWalletRechargePackageDO;
    }

    @Override
    public PayWalletRechargePackageDO convert(WalletRechargePackageUpdateReqVO bean) {
        if ( bean == null ) {
            return null;
        }

        PayWalletRechargePackageDO payWalletRechargePackageDO = new PayWalletRechargePackageDO();

        payWalletRechargePackageDO.setBonusPrice( bean.getBonusPrice() );
        payWalletRechargePackageDO.setId( bean.getId() );
        payWalletRechargePackageDO.setName( bean.getName() );
        payWalletRechargePackageDO.setPayPrice( bean.getPayPrice() );
        if ( bean.getStatus() != null ) {
            payWalletRechargePackageDO.setStatus( bean.getStatus().intValue() );
        }

        return payWalletRechargePackageDO;
    }

    @Override
    public WalletRechargePackageRespVO convert(PayWalletRechargePackageDO bean) {
        if ( bean == null ) {
            return null;
        }

        WalletRechargePackageRespVO walletRechargePackageRespVO = new WalletRechargePackageRespVO();

        walletRechargePackageRespVO.setBonusPrice( bean.getBonusPrice() );
        walletRechargePackageRespVO.setName( bean.getName() );
        walletRechargePackageRespVO.setPayPrice( bean.getPayPrice() );
        if ( bean.getStatus() != null ) {
            walletRechargePackageRespVO.setStatus( bean.getStatus().byteValue() );
        }
        walletRechargePackageRespVO.setCreateTime( bean.getCreateTime() );
        walletRechargePackageRespVO.setId( bean.getId() );

        return walletRechargePackageRespVO;
    }

    @Override
    public List<WalletRechargePackageRespVO> convertList(List<PayWalletRechargePackageDO> list) {
        if ( list == null ) {
            return null;
        }

        List<WalletRechargePackageRespVO> list1 = new ArrayList<WalletRechargePackageRespVO>( list.size() );
        for ( PayWalletRechargePackageDO payWalletRechargePackageDO : list ) {
            list1.add( convert( payWalletRechargePackageDO ) );
        }

        return list1;
    }

    @Override
    public PageResult<WalletRechargePackageRespVO> convertPage(PageResult<PayWalletRechargePackageDO> page) {
        if ( page == null ) {
            return null;
        }

        PageResult<WalletRechargePackageRespVO> pageResult = new PageResult<WalletRechargePackageRespVO>();

        pageResult.setList( convertList( page.getList() ) );
        pageResult.setTotal( page.getTotal() );

        return pageResult;
    }
}
