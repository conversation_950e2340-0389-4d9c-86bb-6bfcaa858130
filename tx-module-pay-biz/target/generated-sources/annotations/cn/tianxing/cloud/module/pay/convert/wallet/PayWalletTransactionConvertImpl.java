package cn.tianxing.cloud.module.pay.convert.wallet;

import cn.tianxing.cloud.framework.common.pojo.PageResult;
import cn.tianxing.cloud.module.pay.controller.admin.wallet.vo.transaction.PayWalletTransactionRespVO;
import cn.tianxing.cloud.module.pay.dal.dataobject.wallet.PayWalletTransactionDO;
import cn.tianxing.cloud.module.pay.service.wallet.bo.WalletTransactionCreateReqBO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-18T16:56:03+0800",
    comments = "version: 1.6.3, compiler: Eclipse JDT (IDE) 3.42.50.v20250729-0351, environment: Java 21.0.8 (Eclipse Adoptium)"
)
public class PayWalletTransactionConvertImpl implements PayWalletTransactionConvert {

    @Override
    public PageResult<PayWalletTransactionRespVO> convertPage2(PageResult<PayWalletTransactionDO> page) {
        if ( page == null ) {
            return null;
        }

        PageResult<PayWalletTransactionRespVO> pageResult = new PageResult<PayWalletTransactionRespVO>();

        pageResult.setList( payWalletTransactionDOListToPayWalletTransactionRespVOList( page.getList() ) );
        pageResult.setTotal( page.getTotal() );

        return pageResult;
    }

    @Override
    public PayWalletTransactionDO convert(WalletTransactionCreateReqBO bean) {
        if ( bean == null ) {
            return null;
        }

        PayWalletTransactionDO payWalletTransactionDO = new PayWalletTransactionDO();

        payWalletTransactionDO.setBalance( bean.getBalance() );
        payWalletTransactionDO.setBizId( bean.getBizId() );
        payWalletTransactionDO.setBizType( bean.getBizType() );
        payWalletTransactionDO.setPrice( bean.getPrice() );
        payWalletTransactionDO.setTitle( bean.getTitle() );
        payWalletTransactionDO.setWalletId( bean.getWalletId() );

        return payWalletTransactionDO;
    }

    protected PayWalletTransactionRespVO payWalletTransactionDOToPayWalletTransactionRespVO(PayWalletTransactionDO payWalletTransactionDO) {
        if ( payWalletTransactionDO == null ) {
            return null;
        }

        PayWalletTransactionRespVO payWalletTransactionRespVO = new PayWalletTransactionRespVO();

        if ( payWalletTransactionDO.getBalance() != null ) {
            payWalletTransactionRespVO.setBalance( payWalletTransactionDO.getBalance().longValue() );
        }
        payWalletTransactionRespVO.setBizType( payWalletTransactionDO.getBizType() );
        payWalletTransactionRespVO.setCreateTime( payWalletTransactionDO.getCreateTime() );
        payWalletTransactionRespVO.setId( payWalletTransactionDO.getId() );
        if ( payWalletTransactionDO.getPrice() != null ) {
            payWalletTransactionRespVO.setPrice( payWalletTransactionDO.getPrice().longValue() );
        }
        payWalletTransactionRespVO.setTitle( payWalletTransactionDO.getTitle() );
        payWalletTransactionRespVO.setWalletId( payWalletTransactionDO.getWalletId() );

        return payWalletTransactionRespVO;
    }

    protected List<PayWalletTransactionRespVO> payWalletTransactionDOListToPayWalletTransactionRespVOList(List<PayWalletTransactionDO> list) {
        if ( list == null ) {
            return null;
        }

        List<PayWalletTransactionRespVO> list1 = new ArrayList<PayWalletTransactionRespVO>( list.size() );
        for ( PayWalletTransactionDO payWalletTransactionDO : list ) {
            list1.add( payWalletTransactionDOToPayWalletTransactionRespVO( payWalletTransactionDO ) );
        }

        return list1;
    }
}
