package cn.tianxing.cloud.module.pay.convert.demo;

import cn.tianxing.cloud.framework.common.pojo.PageResult;
import cn.tianxing.cloud.module.pay.controller.admin.demo.vo.order.PayDemoOrderCreateReqVO;
import cn.tianxing.cloud.module.pay.controller.admin.demo.vo.order.PayDemoOrderRespVO;
import cn.tianxing.cloud.module.pay.dal.dataobject.demo.PayDemoOrderDO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-18T16:56:03+0800",
    comments = "version: 1.6.3, compiler: Eclipse JDT (IDE) 3.42.50.v20250729-0351, environment: Java 21.0.8 (Eclipse Adoptium)"
)
public class PayDemoOrderConvertImpl implements PayDemoOrderConvert {

    @Override
    public PayDemoOrderDO convert(PayDemoOrderCreateReqVO bean) {
        if ( bean == null ) {
            return null;
        }

        PayDemoOrderDO.PayDemoOrderDOBuilder payDemoOrderDO = PayDemoOrderDO.builder();

        payDemoOrderDO.spuId( bean.getSpuId() );

        return payDemoOrderDO.build();
    }

    @Override
    public PayDemoOrderRespVO convert(PayDemoOrderDO bean) {
        if ( bean == null ) {
            return null;
        }

        PayDemoOrderRespVO payDemoOrderRespVO = new PayDemoOrderRespVO();

        payDemoOrderRespVO.setCreateTime( bean.getCreateTime() );
        payDemoOrderRespVO.setId( bean.getId() );
        payDemoOrderRespVO.setPayChannelCode( bean.getPayChannelCode() );
        payDemoOrderRespVO.setPayOrderId( bean.getPayOrderId() );
        payDemoOrderRespVO.setPayRefundId( bean.getPayRefundId() );
        payDemoOrderRespVO.setPayStatus( bean.getPayStatus() );
        payDemoOrderRespVO.setPayTime( bean.getPayTime() );
        payDemoOrderRespVO.setPrice( bean.getPrice() );
        payDemoOrderRespVO.setRefundPrice( bean.getRefundPrice() );
        payDemoOrderRespVO.setRefundTime( bean.getRefundTime() );
        payDemoOrderRespVO.setSpuId( bean.getSpuId() );
        payDemoOrderRespVO.setSpuName( bean.getSpuName() );
        payDemoOrderRespVO.setUserId( bean.getUserId() );

        return payDemoOrderRespVO;
    }

    @Override
    public PageResult<PayDemoOrderRespVO> convertPage(PageResult<PayDemoOrderDO> page) {
        if ( page == null ) {
            return null;
        }

        PageResult<PayDemoOrderRespVO> pageResult = new PageResult<PayDemoOrderRespVO>();

        pageResult.setList( payDemoOrderDOListToPayDemoOrderRespVOList( page.getList() ) );
        pageResult.setTotal( page.getTotal() );

        return pageResult;
    }

    protected List<PayDemoOrderRespVO> payDemoOrderDOListToPayDemoOrderRespVOList(List<PayDemoOrderDO> list) {
        if ( list == null ) {
            return null;
        }

        List<PayDemoOrderRespVO> list1 = new ArrayList<PayDemoOrderRespVO>( list.size() );
        for ( PayDemoOrderDO payDemoOrderDO : list ) {
            list1.add( convert( payDemoOrderDO ) );
        }

        return list1;
    }
}
