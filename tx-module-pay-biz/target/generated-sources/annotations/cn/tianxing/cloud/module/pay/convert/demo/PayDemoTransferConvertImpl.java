package cn.tianxing.cloud.module.pay.convert.demo;

import cn.tianxing.cloud.framework.common.pojo.PageResult;
import cn.tianxing.cloud.module.pay.controller.admin.demo.vo.transfer.PayDemoTransferCreateReqVO;
import cn.tianxing.cloud.module.pay.controller.admin.demo.vo.transfer.PayDemoTransferRespVO;
import cn.tianxing.cloud.module.pay.dal.dataobject.demo.PayDemoTransferDO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-18T16:56:03+0800",
    comments = "version: 1.6.3, compiler: Eclipse JDT (IDE) 3.42.50.v20250729-0351, environment: Java 21.0.8 (Eclipse Adoptium)"
)
public class PayDemoTransferConvertImpl implements PayDemoTransferConvert {

    @Override
    public PayDemoTransferDO convert(PayDemoTransferCreateReqVO bean) {
        if ( bean == null ) {
            return null;
        }

        PayDemoTransferDO payDemoTransferDO = new PayDemoTransferDO();

        payDemoTransferDO.setAlipayLogonId( bean.getAlipayLogonId() );
        payDemoTransferDO.setOpenid( bean.getOpenid() );
        payDemoTransferDO.setPrice( bean.getPrice() );
        payDemoTransferDO.setType( bean.getType() );
        payDemoTransferDO.setUserName( bean.getUserName() );

        return payDemoTransferDO;
    }

    @Override
    public PageResult<PayDemoTransferRespVO> convertPage(PageResult<PayDemoTransferDO> pageResult) {
        if ( pageResult == null ) {
            return null;
        }

        PageResult<PayDemoTransferRespVO> pageResult1 = new PageResult<PayDemoTransferRespVO>();

        pageResult1.setList( payDemoTransferDOListToPayDemoTransferRespVOList( pageResult.getList() ) );
        pageResult1.setTotal( pageResult.getTotal() );

        return pageResult1;
    }

    protected PayDemoTransferRespVO payDemoTransferDOToPayDemoTransferRespVO(PayDemoTransferDO payDemoTransferDO) {
        if ( payDemoTransferDO == null ) {
            return null;
        }

        PayDemoTransferRespVO payDemoTransferRespVO = new PayDemoTransferRespVO();

        payDemoTransferRespVO.setAlipayLogonId( payDemoTransferDO.getAlipayLogonId() );
        payDemoTransferRespVO.setAppId( payDemoTransferDO.getAppId() );
        payDemoTransferRespVO.setId( payDemoTransferDO.getId() );
        payDemoTransferRespVO.setOpenid( payDemoTransferDO.getOpenid() );
        payDemoTransferRespVO.setPayChannelCode( payDemoTransferDO.getPayChannelCode() );
        payDemoTransferRespVO.setPayTransferId( payDemoTransferDO.getPayTransferId() );
        payDemoTransferRespVO.setPrice( payDemoTransferDO.getPrice() );
        payDemoTransferRespVO.setTransferStatus( payDemoTransferDO.getTransferStatus() );
        payDemoTransferRespVO.setTransferTime( payDemoTransferDO.getTransferTime() );
        payDemoTransferRespVO.setType( payDemoTransferDO.getType() );
        payDemoTransferRespVO.setUserName( payDemoTransferDO.getUserName() );

        return payDemoTransferRespVO;
    }

    protected List<PayDemoTransferRespVO> payDemoTransferDOListToPayDemoTransferRespVOList(List<PayDemoTransferDO> list) {
        if ( list == null ) {
            return null;
        }

        List<PayDemoTransferRespVO> list1 = new ArrayList<PayDemoTransferRespVO>( list.size() );
        for ( PayDemoTransferDO payDemoTransferDO : list ) {
            list1.add( payDemoTransferDOToPayDemoTransferRespVO( payDemoTransferDO ) );
        }

        return list1;
    }
}
