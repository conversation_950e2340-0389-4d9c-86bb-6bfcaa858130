package cn.tianxing.cloud.module.pay.convert.refund;

import cn.tianxing.cloud.framework.common.pojo.PageResult;
import cn.tianxing.cloud.module.pay.api.refund.dto.PayRefundCreateReqDTO;
import cn.tianxing.cloud.module.pay.api.refund.dto.PayRefundRespDTO;
import cn.tianxing.cloud.module.pay.controller.admin.refund.vo.PayRefundDetailsRespVO;
import cn.tianxing.cloud.module.pay.controller.admin.refund.vo.PayRefundExcelVO;
import cn.tianxing.cloud.module.pay.controller.admin.refund.vo.PayRefundPageItemRespVO;
import cn.tianxing.cloud.module.pay.dal.dataobject.order.PayOrderDO;
import cn.tianxing.cloud.module.pay.dal.dataobject.refund.PayRefundDO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-18T16:56:03+0800",
    comments = "version: 1.6.3, compiler: Eclipse JDT (IDE) 3.42.50.v20250729-0351, environment: Java 21.0.8 (Eclipse Adoptium)"
)
public class PayRefundConvertImpl implements PayRefundConvert {

    @Override
    public PayRefundDetailsRespVO convert(PayRefundDO bean) {
        if ( bean == null ) {
            return null;
        }

        PayRefundDetailsRespVO payRefundDetailsRespVO = new PayRefundDetailsRespVO();

        payRefundDetailsRespVO.setAppId( bean.getAppId() );
        payRefundDetailsRespVO.setChannelCode( bean.getChannelCode() );
        payRefundDetailsRespVO.setChannelErrorCode( bean.getChannelErrorCode() );
        payRefundDetailsRespVO.setChannelErrorMsg( bean.getChannelErrorMsg() );
        payRefundDetailsRespVO.setChannelId( bean.getChannelId() );
        payRefundDetailsRespVO.setChannelNotifyData( bean.getChannelNotifyData() );
        payRefundDetailsRespVO.setChannelOrderNo( bean.getChannelOrderNo() );
        payRefundDetailsRespVO.setChannelRefundNo( bean.getChannelRefundNo() );
        payRefundDetailsRespVO.setMerchantOrderId( bean.getMerchantOrderId() );
        payRefundDetailsRespVO.setMerchantRefundId( bean.getMerchantRefundId() );
        payRefundDetailsRespVO.setNo( bean.getNo() );
        payRefundDetailsRespVO.setNotifyUrl( bean.getNotifyUrl() );
        payRefundDetailsRespVO.setOrderId( bean.getOrderId() );
        if ( bean.getPayPrice() != null ) {
            payRefundDetailsRespVO.setPayPrice( bean.getPayPrice().longValue() );
        }
        payRefundDetailsRespVO.setReason( bean.getReason() );
        if ( bean.getRefundPrice() != null ) {
            payRefundDetailsRespVO.setRefundPrice( bean.getRefundPrice().longValue() );
        }
        payRefundDetailsRespVO.setStatus( bean.getStatus() );
        payRefundDetailsRespVO.setSuccessTime( bean.getSuccessTime() );
        payRefundDetailsRespVO.setUserIp( bean.getUserIp() );
        payRefundDetailsRespVO.setCreateTime( bean.getCreateTime() );
        payRefundDetailsRespVO.setId( bean.getId() );
        payRefundDetailsRespVO.setUpdateTime( bean.getUpdateTime() );

        return payRefundDetailsRespVO;
    }

    @Override
    public PayRefundDetailsRespVO.Order convert(PayOrderDO bean) {
        if ( bean == null ) {
            return null;
        }

        PayRefundDetailsRespVO.Order order = new PayRefundDetailsRespVO.Order();

        order.setSubject( bean.getSubject() );

        return order;
    }

    @Override
    public PageResult<PayRefundPageItemRespVO> convertPage(PageResult<PayRefundDO> page) {
        if ( page == null ) {
            return null;
        }

        PageResult<PayRefundPageItemRespVO> pageResult = new PageResult<PayRefundPageItemRespVO>();

        pageResult.setList( payRefundDOListToPayRefundPageItemRespVOList( page.getList() ) );
        pageResult.setTotal( page.getTotal() );

        return pageResult;
    }

    @Override
    public PayRefundDO convert(PayRefundCreateReqDTO bean) {
        if ( bean == null ) {
            return null;
        }

        PayRefundDO.PayRefundDOBuilder payRefundDO = PayRefundDO.builder();

        payRefundDO.merchantOrderId( bean.getMerchantOrderId() );
        payRefundDO.merchantRefundId( bean.getMerchantRefundId() );
        payRefundDO.reason( bean.getReason() );
        payRefundDO.userIp( bean.getUserIp() );

        return payRefundDO.build();
    }

    @Override
    public PayRefundRespDTO convert02(PayRefundDO bean) {
        if ( bean == null ) {
            return null;
        }

        PayRefundRespDTO payRefundRespDTO = new PayRefundRespDTO();

        payRefundRespDTO.setId( bean.getId() );
        payRefundRespDTO.setMerchantOrderId( bean.getMerchantOrderId() );
        payRefundRespDTO.setRefundPrice( bean.getRefundPrice() );
        payRefundRespDTO.setStatus( bean.getStatus() );
        payRefundRespDTO.setSuccessTime( bean.getSuccessTime() );

        return payRefundRespDTO;
    }

    @Override
    public PayRefundExcelVO convertExcel(PayRefundDO bean) {
        if ( bean == null ) {
            return null;
        }

        PayRefundExcelVO payRefundExcelVO = new PayRefundExcelVO();

        payRefundExcelVO.setChannelCode( bean.getChannelCode() );
        payRefundExcelVO.setChannelOrderNo( bean.getChannelOrderNo() );
        payRefundExcelVO.setChannelRefundNo( bean.getChannelRefundNo() );
        payRefundExcelVO.setCreateTime( bean.getCreateTime() );
        payRefundExcelVO.setId( bean.getId() );
        payRefundExcelVO.setMerchantOrderId( bean.getMerchantOrderId() );
        payRefundExcelVO.setMerchantRefundId( bean.getMerchantRefundId() );
        payRefundExcelVO.setNo( bean.getNo() );
        payRefundExcelVO.setPayPrice( bean.getPayPrice() );
        payRefundExcelVO.setReason( bean.getReason() );
        payRefundExcelVO.setRefundPrice( bean.getRefundPrice() );
        payRefundExcelVO.setStatus( bean.getStatus() );
        payRefundExcelVO.setSuccessTime( bean.getSuccessTime() );

        return payRefundExcelVO;
    }

    protected PayRefundPageItemRespVO payRefundDOToPayRefundPageItemRespVO(PayRefundDO payRefundDO) {
        if ( payRefundDO == null ) {
            return null;
        }

        PayRefundPageItemRespVO payRefundPageItemRespVO = new PayRefundPageItemRespVO();

        payRefundPageItemRespVO.setAppId( payRefundDO.getAppId() );
        payRefundPageItemRespVO.setChannelCode( payRefundDO.getChannelCode() );
        payRefundPageItemRespVO.setChannelErrorCode( payRefundDO.getChannelErrorCode() );
        payRefundPageItemRespVO.setChannelErrorMsg( payRefundDO.getChannelErrorMsg() );
        payRefundPageItemRespVO.setChannelId( payRefundDO.getChannelId() );
        payRefundPageItemRespVO.setChannelNotifyData( payRefundDO.getChannelNotifyData() );
        payRefundPageItemRespVO.setChannelOrderNo( payRefundDO.getChannelOrderNo() );
        payRefundPageItemRespVO.setChannelRefundNo( payRefundDO.getChannelRefundNo() );
        payRefundPageItemRespVO.setMerchantOrderId( payRefundDO.getMerchantOrderId() );
        payRefundPageItemRespVO.setMerchantRefundId( payRefundDO.getMerchantRefundId() );
        payRefundPageItemRespVO.setNo( payRefundDO.getNo() );
        payRefundPageItemRespVO.setNotifyUrl( payRefundDO.getNotifyUrl() );
        payRefundPageItemRespVO.setOrderId( payRefundDO.getOrderId() );
        if ( payRefundDO.getPayPrice() != null ) {
            payRefundPageItemRespVO.setPayPrice( payRefundDO.getPayPrice().longValue() );
        }
        payRefundPageItemRespVO.setReason( payRefundDO.getReason() );
        if ( payRefundDO.getRefundPrice() != null ) {
            payRefundPageItemRespVO.setRefundPrice( payRefundDO.getRefundPrice().longValue() );
        }
        payRefundPageItemRespVO.setStatus( payRefundDO.getStatus() );
        payRefundPageItemRespVO.setSuccessTime( payRefundDO.getSuccessTime() );
        payRefundPageItemRespVO.setUserIp( payRefundDO.getUserIp() );
        payRefundPageItemRespVO.setCreateTime( payRefundDO.getCreateTime() );
        payRefundPageItemRespVO.setId( payRefundDO.getId() );

        return payRefundPageItemRespVO;
    }

    protected List<PayRefundPageItemRespVO> payRefundDOListToPayRefundPageItemRespVOList(List<PayRefundDO> list) {
        if ( list == null ) {
            return null;
        }

        List<PayRefundPageItemRespVO> list1 = new ArrayList<PayRefundPageItemRespVO>( list.size() );
        for ( PayRefundDO payRefundDO : list ) {
            list1.add( payRefundDOToPayRefundPageItemRespVO( payRefundDO ) );
        }

        return list1;
    }
}
