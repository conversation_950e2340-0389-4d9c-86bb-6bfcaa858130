package cn.tianxing.cloud.module.pay.convert.channel;

import cn.tianxing.cloud.framework.common.pojo.PageResult;
import cn.tianxing.cloud.module.pay.controller.admin.channel.vo.PayChannelCreateReqVO;
import cn.tianxing.cloud.module.pay.controller.admin.channel.vo.PayChannelRespVO;
import cn.tianxing.cloud.module.pay.controller.admin.channel.vo.PayChannelUpdateReqVO;
import cn.tianxing.cloud.module.pay.dal.dataobject.channel.PayChannelDO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-18T16:56:03+0800",
    comments = "version: 1.6.3, compiler: Eclipse JDT (IDE) 3.42.50.v20250729-0351, environment: Java 21.0.8 (Eclipse Adoptium)"
)
public class PayChannelConvertImpl implements PayChannelConvert {

    @Override
    public PayChannelDO convert(PayChannelCreateReqVO bean) {
        if ( bean == null ) {
            return null;
        }

        PayChannelDO.PayChannelDOBuilder payChannelDO = PayChannelDO.builder();

        payChannelDO.appId( bean.getAppId() );
        payChannelDO.code( bean.getCode() );
        payChannelDO.feeRate( bean.getFeeRate() );
        payChannelDO.remark( bean.getRemark() );
        payChannelDO.status( bean.getStatus() );

        return payChannelDO.build();
    }

    @Override
    public PayChannelDO convert(PayChannelUpdateReqVO bean) {
        if ( bean == null ) {
            return null;
        }

        PayChannelDO.PayChannelDOBuilder payChannelDO = PayChannelDO.builder();

        payChannelDO.appId( bean.getAppId() );
        payChannelDO.feeRate( bean.getFeeRate() );
        payChannelDO.id( bean.getId() );
        payChannelDO.remark( bean.getRemark() );
        payChannelDO.status( bean.getStatus() );

        return payChannelDO.build();
    }

    @Override
    public PayChannelRespVO convert(PayChannelDO bean) {
        if ( bean == null ) {
            return null;
        }

        PayChannelRespVO payChannelRespVO = new PayChannelRespVO();

        payChannelRespVO.setAppId( bean.getAppId() );
        payChannelRespVO.setFeeRate( bean.getFeeRate() );
        payChannelRespVO.setRemark( bean.getRemark() );
        payChannelRespVO.setStatus( bean.getStatus() );
        payChannelRespVO.setCode( bean.getCode() );
        payChannelRespVO.setCreateTime( bean.getCreateTime() );
        payChannelRespVO.setId( bean.getId() );

        payChannelRespVO.setConfig( cn.tianxing.cloud.framework.common.util.json.JsonUtils.toJsonString(bean.getConfig()) );

        return payChannelRespVO;
    }

    @Override
    public PageResult<PayChannelRespVO> convertPage(PageResult<PayChannelDO> page) {
        if ( page == null ) {
            return null;
        }

        PageResult<PayChannelRespVO> pageResult = new PageResult<PayChannelRespVO>();

        pageResult.setList( payChannelDOListToPayChannelRespVOList( page.getList() ) );
        pageResult.setTotal( page.getTotal() );

        return pageResult;
    }

    protected List<PayChannelRespVO> payChannelDOListToPayChannelRespVOList(List<PayChannelDO> list) {
        if ( list == null ) {
            return null;
        }

        List<PayChannelRespVO> list1 = new ArrayList<PayChannelRespVO>( list.size() );
        for ( PayChannelDO payChannelDO : list ) {
            list1.add( convert( payChannelDO ) );
        }

        return list1;
    }
}
