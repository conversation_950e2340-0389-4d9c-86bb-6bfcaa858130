package cn.tianxing.cloud.module.pay.convert.notify;

import cn.tianxing.cloud.framework.common.pojo.PageResult;
import cn.tianxing.cloud.module.pay.controller.admin.notify.vo.PayNotifyTaskDetailRespVO;
import cn.tianxing.cloud.module.pay.controller.admin.notify.vo.PayNotifyTaskRespVO;
import cn.tianxing.cloud.module.pay.dal.dataobject.notify.PayNotifyLogDO;
import cn.tianxing.cloud.module.pay.dal.dataobject.notify.PayNotifyTaskDO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-18T16:56:03+0800",
    comments = "version: 1.6.3, compiler: Eclipse JDT (IDE) 3.42.50.v20250729-0351, environment: Java 21.0.8 (Eclipse Adoptium)"
)
public class PayNotifyTaskConvertImpl implements PayNotifyTaskConvert {

    @Override
    public PayNotifyTaskRespVO convert(PayNotifyTaskDO bean) {
        if ( bean == null ) {
            return null;
        }

        PayNotifyTaskRespVO payNotifyTaskRespVO = new PayNotifyTaskRespVO();

        payNotifyTaskRespVO.setAppId( bean.getAppId() );
        payNotifyTaskRespVO.setDataId( bean.getDataId() );
        payNotifyTaskRespVO.setLastExecuteTime( bean.getLastExecuteTime() );
        if ( bean.getMaxNotifyTimes() != null ) {
            payNotifyTaskRespVO.setMaxNotifyTimes( bean.getMaxNotifyTimes().byteValue() );
        }
        payNotifyTaskRespVO.setMerchantOrderId( bean.getMerchantOrderId() );
        payNotifyTaskRespVO.setNextNotifyTime( bean.getNextNotifyTime() );
        if ( bean.getNotifyTimes() != null ) {
            payNotifyTaskRespVO.setNotifyTimes( bean.getNotifyTimes().byteValue() );
        }
        payNotifyTaskRespVO.setNotifyUrl( bean.getNotifyUrl() );
        if ( bean.getStatus() != null ) {
            payNotifyTaskRespVO.setStatus( bean.getStatus().byteValue() );
        }
        if ( bean.getType() != null ) {
            payNotifyTaskRespVO.setType( bean.getType().byteValue() );
        }
        payNotifyTaskRespVO.setCreateTime( bean.getCreateTime() );
        payNotifyTaskRespVO.setId( bean.getId() );

        return payNotifyTaskRespVO;
    }

    @Override
    public PageResult<PayNotifyTaskRespVO> convertPage(PageResult<PayNotifyTaskDO> page) {
        if ( page == null ) {
            return null;
        }

        PageResult<PayNotifyTaskRespVO> pageResult = new PageResult<PayNotifyTaskRespVO>();

        pageResult.setList( payNotifyTaskDOListToPayNotifyTaskRespVOList( page.getList() ) );
        pageResult.setTotal( page.getTotal() );

        return pageResult;
    }

    @Override
    public PayNotifyTaskDetailRespVO convert(PayNotifyTaskDO task, List<PayNotifyLogDO> logs) {
        if ( task == null && logs == null ) {
            return null;
        }

        PayNotifyTaskDetailRespVO payNotifyTaskDetailRespVO = new PayNotifyTaskDetailRespVO();

        if ( task != null ) {
            payNotifyTaskDetailRespVO.setAppId( task.getAppId() );
            payNotifyTaskDetailRespVO.setDataId( task.getDataId() );
            payNotifyTaskDetailRespVO.setLastExecuteTime( task.getLastExecuteTime() );
            if ( task.getMaxNotifyTimes() != null ) {
                payNotifyTaskDetailRespVO.setMaxNotifyTimes( task.getMaxNotifyTimes().byteValue() );
            }
            payNotifyTaskDetailRespVO.setMerchantOrderId( task.getMerchantOrderId() );
            payNotifyTaskDetailRespVO.setNextNotifyTime( task.getNextNotifyTime() );
            if ( task.getNotifyTimes() != null ) {
                payNotifyTaskDetailRespVO.setNotifyTimes( task.getNotifyTimes().byteValue() );
            }
            payNotifyTaskDetailRespVO.setNotifyUrl( task.getNotifyUrl() );
            if ( task.getStatus() != null ) {
                payNotifyTaskDetailRespVO.setStatus( task.getStatus().byteValue() );
            }
            if ( task.getType() != null ) {
                payNotifyTaskDetailRespVO.setType( task.getType().byteValue() );
            }
            payNotifyTaskDetailRespVO.setCreateTime( task.getCreateTime() );
            payNotifyTaskDetailRespVO.setId( task.getId() );
            payNotifyTaskDetailRespVO.setUpdateTime( task.getUpdateTime() );
        }
        payNotifyTaskDetailRespVO.setLogs( payNotifyLogDOListToLogList( logs ) );

        return payNotifyTaskDetailRespVO;
    }

    protected List<PayNotifyTaskRespVO> payNotifyTaskDOListToPayNotifyTaskRespVOList(List<PayNotifyTaskDO> list) {
        if ( list == null ) {
            return null;
        }

        List<PayNotifyTaskRespVO> list1 = new ArrayList<PayNotifyTaskRespVO>( list.size() );
        for ( PayNotifyTaskDO payNotifyTaskDO : list ) {
            list1.add( convert( payNotifyTaskDO ) );
        }

        return list1;
    }

    protected PayNotifyTaskDetailRespVO.Log payNotifyLogDOToLog(PayNotifyLogDO payNotifyLogDO) {
        if ( payNotifyLogDO == null ) {
            return null;
        }

        PayNotifyTaskDetailRespVO.Log log = new PayNotifyTaskDetailRespVO.Log();

        log.setCreateTime( payNotifyLogDO.getCreateTime() );
        log.setId( payNotifyLogDO.getId() );
        if ( payNotifyLogDO.getNotifyTimes() != null ) {
            log.setNotifyTimes( payNotifyLogDO.getNotifyTimes().byteValue() );
        }
        log.setResponse( payNotifyLogDO.getResponse() );
        if ( payNotifyLogDO.getStatus() != null ) {
            log.setStatus( payNotifyLogDO.getStatus().byteValue() );
        }

        return log;
    }

    protected List<PayNotifyTaskDetailRespVO.Log> payNotifyLogDOListToLogList(List<PayNotifyLogDO> list) {
        if ( list == null ) {
            return null;
        }

        List<PayNotifyTaskDetailRespVO.Log> list1 = new ArrayList<PayNotifyTaskDetailRespVO.Log>( list.size() );
        for ( PayNotifyLogDO payNotifyLogDO : list ) {
            list1.add( payNotifyLogDOToLog( payNotifyLogDO ) );
        }

        return list1;
    }
}
