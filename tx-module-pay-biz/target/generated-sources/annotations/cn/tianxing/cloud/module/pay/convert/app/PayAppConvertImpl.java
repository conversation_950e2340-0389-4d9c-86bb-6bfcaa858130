package cn.tianxing.cloud.module.pay.convert.app;

import cn.tianxing.cloud.framework.common.pojo.PageResult;
import cn.tianxing.cloud.module.pay.controller.admin.app.vo.PayAppCreateReqVO;
import cn.tianxing.cloud.module.pay.controller.admin.app.vo.PayAppPageItemRespVO;
import cn.tianxing.cloud.module.pay.controller.admin.app.vo.PayAppRespVO;
import cn.tianxing.cloud.module.pay.controller.admin.app.vo.PayAppUpdateReqVO;
import cn.tianxing.cloud.module.pay.dal.dataobject.app.PayAppDO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-18T16:56:03+0800",
    comments = "version: 1.6.3, compiler: Eclipse JDT (IDE) 3.42.50.v20250729-0351, environment: Java 21.0.8 (Eclipse Adoptium)"
)
public class PayAppConvertImpl implements PayAppConvert {

    @Override
    public PayAppPageItemRespVO pageConvert(PayAppDO bean) {
        if ( bean == null ) {
            return null;
        }

        PayAppPageItemRespVO payAppPageItemRespVO = new PayAppPageItemRespVO();

        payAppPageItemRespVO.setAppKey( bean.getAppKey() );
        payAppPageItemRespVO.setName( bean.getName() );
        payAppPageItemRespVO.setOrderNotifyUrl( bean.getOrderNotifyUrl() );
        payAppPageItemRespVO.setRefundNotifyUrl( bean.getRefundNotifyUrl() );
        payAppPageItemRespVO.setRemark( bean.getRemark() );
        payAppPageItemRespVO.setStatus( bean.getStatus() );
        payAppPageItemRespVO.setTransferNotifyUrl( bean.getTransferNotifyUrl() );
        payAppPageItemRespVO.setCreateTime( bean.getCreateTime() );
        payAppPageItemRespVO.setId( bean.getId() );

        return payAppPageItemRespVO;
    }

    @Override
    public PayAppDO convert(PayAppCreateReqVO bean) {
        if ( bean == null ) {
            return null;
        }

        PayAppDO.PayAppDOBuilder payAppDO = PayAppDO.builder();

        payAppDO.appKey( bean.getAppKey() );
        payAppDO.name( bean.getName() );
        payAppDO.orderNotifyUrl( bean.getOrderNotifyUrl() );
        payAppDO.refundNotifyUrl( bean.getRefundNotifyUrl() );
        payAppDO.remark( bean.getRemark() );
        payAppDO.status( bean.getStatus() );
        payAppDO.transferNotifyUrl( bean.getTransferNotifyUrl() );

        return payAppDO.build();
    }

    @Override
    public PayAppDO convert(PayAppUpdateReqVO bean) {
        if ( bean == null ) {
            return null;
        }

        PayAppDO.PayAppDOBuilder payAppDO = PayAppDO.builder();

        payAppDO.appKey( bean.getAppKey() );
        payAppDO.id( bean.getId() );
        payAppDO.name( bean.getName() );
        payAppDO.orderNotifyUrl( bean.getOrderNotifyUrl() );
        payAppDO.refundNotifyUrl( bean.getRefundNotifyUrl() );
        payAppDO.remark( bean.getRemark() );
        payAppDO.status( bean.getStatus() );
        payAppDO.transferNotifyUrl( bean.getTransferNotifyUrl() );

        return payAppDO.build();
    }

    @Override
    public PayAppRespVO convert(PayAppDO bean) {
        if ( bean == null ) {
            return null;
        }

        PayAppRespVO payAppRespVO = new PayAppRespVO();

        payAppRespVO.setName( bean.getName() );
        payAppRespVO.setOrderNotifyUrl( bean.getOrderNotifyUrl() );
        payAppRespVO.setRefundNotifyUrl( bean.getRefundNotifyUrl() );
        payAppRespVO.setRemark( bean.getRemark() );
        payAppRespVO.setStatus( bean.getStatus() );
        payAppRespVO.setTransferNotifyUrl( bean.getTransferNotifyUrl() );
        payAppRespVO.setAppKey( bean.getAppKey() );
        payAppRespVO.setCreateTime( bean.getCreateTime() );
        payAppRespVO.setId( bean.getId() );

        return payAppRespVO;
    }

    @Override
    public List<PayAppRespVO> convertList(List<PayAppDO> list) {
        if ( list == null ) {
            return null;
        }

        List<PayAppRespVO> list1 = new ArrayList<PayAppRespVO>( list.size() );
        for ( PayAppDO payAppDO : list ) {
            list1.add( convert( payAppDO ) );
        }

        return list1;
    }

    @Override
    public PageResult<PayAppPageItemRespVO> convertPage(PageResult<PayAppDO> page) {
        if ( page == null ) {
            return null;
        }

        PageResult<PayAppPageItemRespVO> pageResult = new PageResult<PayAppPageItemRespVO>();

        pageResult.setList( payAppDOListToPayAppPageItemRespVOList( page.getList() ) );
        pageResult.setTotal( page.getTotal() );

        return pageResult;
    }

    protected List<PayAppPageItemRespVO> payAppDOListToPayAppPageItemRespVOList(List<PayAppDO> list) {
        if ( list == null ) {
            return null;
        }

        List<PayAppPageItemRespVO> list1 = new ArrayList<PayAppPageItemRespVO>( list.size() );
        for ( PayAppDO payAppDO : list ) {
            list1.add( pageConvert( payAppDO ) );
        }

        return list1;
    }
}
