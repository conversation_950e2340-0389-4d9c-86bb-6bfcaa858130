package cn.tianxing.cloud.module.pay.convert.order;

import cn.tianxing.cloud.framework.common.pojo.PageResult;
import cn.tianxing.cloud.framework.pay.core.client.dto.order.PayOrderUnifiedReqDTO;
import cn.tianxing.cloud.module.pay.api.order.dto.PayOrderCreateReqDTO;
import cn.tianxing.cloud.module.pay.api.order.dto.PayOrderRespDTO;
import cn.tianxing.cloud.module.pay.controller.admin.order.vo.PayOrderDetailsRespVO;
import cn.tianxing.cloud.module.pay.controller.admin.order.vo.PayOrderExcelVO;
import cn.tianxing.cloud.module.pay.controller.admin.order.vo.PayOrderPageItemRespVO;
import cn.tianxing.cloud.module.pay.controller.admin.order.vo.PayOrderRespVO;
import cn.tianxing.cloud.module.pay.controller.admin.order.vo.PayOrderSubmitReqVO;
import cn.tianxing.cloud.module.pay.controller.admin.order.vo.PayOrderSubmitRespVO;
import cn.tianxing.cloud.module.pay.controller.app.order.vo.AppPayOrderSubmitRespVO;
import cn.tianxing.cloud.module.pay.dal.dataobject.order.PayOrderDO;
import cn.tianxing.cloud.module.pay.dal.dataobject.order.PayOrderExtensionDO;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-18T16:56:03+0800",
    comments = "version: 1.6.3, compiler: Eclipse JDT (IDE) 3.42.50.v20250729-0351, environment: Java 21.0.8 (Eclipse Adoptium)"
)
public class PayOrderConvertImpl implements PayOrderConvert {

    @Override
    public PayOrderRespVO convert(PayOrderDO bean) {
        if ( bean == null ) {
            return null;
        }

        PayOrderRespVO payOrderRespVO = new PayOrderRespVO();

        payOrderRespVO.setAppId( bean.getAppId() );
        payOrderRespVO.setBody( bean.getBody() );
        payOrderRespVO.setChannelCode( bean.getChannelCode() );
        payOrderRespVO.setChannelFeePrice( bean.getChannelFeePrice() );
        payOrderRespVO.setChannelFeeRate( bean.getChannelFeeRate() );
        payOrderRespVO.setChannelId( bean.getChannelId() );
        payOrderRespVO.setChannelOrderNo( bean.getChannelOrderNo() );
        payOrderRespVO.setChannelUserId( bean.getChannelUserId() );
        payOrderRespVO.setExpireTime( bean.getExpireTime() );
        payOrderRespVO.setExtensionId( bean.getExtensionId() );
        payOrderRespVO.setMerchantOrderId( bean.getMerchantOrderId() );
        payOrderRespVO.setNo( bean.getNo() );
        payOrderRespVO.setNotifyUrl( bean.getNotifyUrl() );
        if ( bean.getPrice() != null ) {
            payOrderRespVO.setPrice( bean.getPrice().longValue() );
        }
        if ( bean.getRefundPrice() != null ) {
            payOrderRespVO.setRefundPrice( bean.getRefundPrice().longValue() );
        }
        payOrderRespVO.setStatus( bean.getStatus() );
        payOrderRespVO.setSubject( bean.getSubject() );
        payOrderRespVO.setSuccessTime( bean.getSuccessTime() );
        payOrderRespVO.setUserIp( bean.getUserIp() );
        payOrderRespVO.setCreateTime( bean.getCreateTime() );
        payOrderRespVO.setId( bean.getId() );

        return payOrderRespVO;
    }

    @Override
    public PayOrderRespDTO convert2(PayOrderDO order) {
        if ( order == null ) {
            return null;
        }

        PayOrderRespDTO payOrderRespDTO = new PayOrderRespDTO();

        payOrderRespDTO.setChannelCode( order.getChannelCode() );
        payOrderRespDTO.setId( order.getId() );
        payOrderRespDTO.setMerchantOrderId( order.getMerchantOrderId() );
        payOrderRespDTO.setPrice( order.getPrice() );
        payOrderRespDTO.setStatus( order.getStatus() );

        return payOrderRespDTO;
    }

    @Override
    public PayOrderDetailsRespVO convertDetail(PayOrderDO bean) {
        if ( bean == null ) {
            return null;
        }

        PayOrderDetailsRespVO payOrderDetailsRespVO = new PayOrderDetailsRespVO();

        payOrderDetailsRespVO.setAppId( bean.getAppId() );
        payOrderDetailsRespVO.setBody( bean.getBody() );
        payOrderDetailsRespVO.setChannelCode( bean.getChannelCode() );
        payOrderDetailsRespVO.setChannelFeePrice( bean.getChannelFeePrice() );
        payOrderDetailsRespVO.setChannelFeeRate( bean.getChannelFeeRate() );
        payOrderDetailsRespVO.setChannelId( bean.getChannelId() );
        payOrderDetailsRespVO.setChannelOrderNo( bean.getChannelOrderNo() );
        payOrderDetailsRespVO.setChannelUserId( bean.getChannelUserId() );
        payOrderDetailsRespVO.setExpireTime( bean.getExpireTime() );
        payOrderDetailsRespVO.setExtensionId( bean.getExtensionId() );
        payOrderDetailsRespVO.setMerchantOrderId( bean.getMerchantOrderId() );
        payOrderDetailsRespVO.setNo( bean.getNo() );
        payOrderDetailsRespVO.setNotifyUrl( bean.getNotifyUrl() );
        if ( bean.getPrice() != null ) {
            payOrderDetailsRespVO.setPrice( bean.getPrice().longValue() );
        }
        if ( bean.getRefundPrice() != null ) {
            payOrderDetailsRespVO.setRefundPrice( bean.getRefundPrice().longValue() );
        }
        payOrderDetailsRespVO.setStatus( bean.getStatus() );
        payOrderDetailsRespVO.setSubject( bean.getSubject() );
        payOrderDetailsRespVO.setSuccessTime( bean.getSuccessTime() );
        payOrderDetailsRespVO.setUserIp( bean.getUserIp() );
        payOrderDetailsRespVO.setCreateTime( bean.getCreateTime() );
        payOrderDetailsRespVO.setId( bean.getId() );
        payOrderDetailsRespVO.setUpdateTime( bean.getUpdateTime() );

        return payOrderDetailsRespVO;
    }

    @Override
    public PayOrderDetailsRespVO.PayOrderExtension convert(PayOrderExtensionDO bean) {
        if ( bean == null ) {
            return null;
        }

        PayOrderDetailsRespVO.PayOrderExtension payOrderExtension = new PayOrderDetailsRespVO.PayOrderExtension();

        payOrderExtension.setChannelNotifyData( bean.getChannelNotifyData() );
        payOrderExtension.setNo( bean.getNo() );

        return payOrderExtension;
    }

    @Override
    public PageResult<PayOrderPageItemRespVO> convertPage(PageResult<PayOrderDO> page) {
        if ( page == null ) {
            return null;
        }

        PageResult<PayOrderPageItemRespVO> pageResult = new PageResult<PayOrderPageItemRespVO>();

        pageResult.setList( payOrderDOListToPayOrderPageItemRespVOList( page.getList() ) );
        pageResult.setTotal( page.getTotal() );

        return pageResult;
    }

    @Override
    public PayOrderExcelVO convertExcel(PayOrderDO bean) {
        if ( bean == null ) {
            return null;
        }

        PayOrderExcelVO payOrderExcelVO = new PayOrderExcelVO();

        payOrderExcelVO.setBody( bean.getBody() );
        payOrderExcelVO.setChannelCode( bean.getChannelCode() );
        payOrderExcelVO.setChannelFeePrice( bean.getChannelFeePrice() );
        payOrderExcelVO.setChannelOrderNo( bean.getChannelOrderNo() );
        payOrderExcelVO.setCreateTime( bean.getCreateTime() );
        payOrderExcelVO.setExpireTime( bean.getExpireTime() );
        payOrderExcelVO.setId( bean.getId() );
        payOrderExcelVO.setMerchantOrderId( bean.getMerchantOrderId() );
        payOrderExcelVO.setNo( bean.getNo() );
        payOrderExcelVO.setPrice( bean.getPrice() );
        payOrderExcelVO.setRefundPrice( bean.getRefundPrice() );
        payOrderExcelVO.setStatus( bean.getStatus() );
        payOrderExcelVO.setSubject( bean.getSubject() );
        payOrderExcelVO.setSuccessTime( bean.getSuccessTime() );

        return payOrderExcelVO;
    }

    @Override
    public PayOrderDO convert(PayOrderCreateReqDTO bean) {
        if ( bean == null ) {
            return null;
        }

        PayOrderDO.PayOrderDOBuilder payOrderDO = PayOrderDO.builder();

        payOrderDO.body( bean.getBody() );
        payOrderDO.expireTime( bean.getExpireTime() );
        payOrderDO.merchantOrderId( bean.getMerchantOrderId() );
        payOrderDO.price( bean.getPrice() );
        payOrderDO.subject( bean.getSubject() );
        payOrderDO.userIp( bean.getUserIp() );

        return payOrderDO.build();
    }

    @Override
    public PayOrderExtensionDO convert(PayOrderSubmitReqVO bean, String userIp) {
        if ( bean == null && userIp == null ) {
            return null;
        }

        PayOrderExtensionDO.PayOrderExtensionDOBuilder payOrderExtensionDO = PayOrderExtensionDO.builder();

        if ( bean != null ) {
            payOrderExtensionDO.channelCode( bean.getChannelCode() );
            Map<String, String> map = bean.getChannelExtras();
            if ( map != null ) {
                payOrderExtensionDO.channelExtras( new LinkedHashMap<String, String>( map ) );
            }
        }
        payOrderExtensionDO.userIp( userIp );

        return payOrderExtensionDO.build();
    }

    @Override
    public PayOrderUnifiedReqDTO convert2(PayOrderSubmitReqVO reqVO, String userIp) {
        if ( reqVO == null && userIp == null ) {
            return null;
        }

        PayOrderUnifiedReqDTO payOrderUnifiedReqDTO = new PayOrderUnifiedReqDTO();

        if ( reqVO != null ) {
            Map<String, String> map = reqVO.getChannelExtras();
            if ( map != null ) {
                payOrderUnifiedReqDTO.setChannelExtras( new LinkedHashMap<String, String>( map ) );
            }
            payOrderUnifiedReqDTO.setDisplayMode( reqVO.getDisplayMode() );
            payOrderUnifiedReqDTO.setReturnUrl( reqVO.getReturnUrl() );
        }
        payOrderUnifiedReqDTO.setUserIp( userIp );

        return payOrderUnifiedReqDTO;
    }

    @Override
    public PayOrderSubmitRespVO convert(PayOrderDO order, cn.tianxing.cloud.framework.pay.core.client.dto.order.PayOrderRespDTO respDTO) {
        if ( order == null && respDTO == null ) {
            return null;
        }

        PayOrderSubmitRespVO payOrderSubmitRespVO = new PayOrderSubmitRespVO();

        if ( order != null ) {
            payOrderSubmitRespVO.setStatus( order.getStatus() );
        }
        if ( respDTO != null ) {
            payOrderSubmitRespVO.setDisplayContent( respDTO.getDisplayContent() );
            payOrderSubmitRespVO.setDisplayMode( respDTO.getDisplayMode() );
        }

        return payOrderSubmitRespVO;
    }

    @Override
    public AppPayOrderSubmitRespVO convert3(PayOrderSubmitRespVO bean) {
        if ( bean == null ) {
            return null;
        }

        AppPayOrderSubmitRespVO appPayOrderSubmitRespVO = new AppPayOrderSubmitRespVO();

        appPayOrderSubmitRespVO.setDisplayContent( bean.getDisplayContent() );
        appPayOrderSubmitRespVO.setDisplayMode( bean.getDisplayMode() );
        appPayOrderSubmitRespVO.setStatus( bean.getStatus() );

        return appPayOrderSubmitRespVO;
    }

    protected PayOrderPageItemRespVO payOrderDOToPayOrderPageItemRespVO(PayOrderDO payOrderDO) {
        if ( payOrderDO == null ) {
            return null;
        }

        PayOrderPageItemRespVO payOrderPageItemRespVO = new PayOrderPageItemRespVO();

        payOrderPageItemRespVO.setAppId( payOrderDO.getAppId() );
        payOrderPageItemRespVO.setBody( payOrderDO.getBody() );
        payOrderPageItemRespVO.setChannelCode( payOrderDO.getChannelCode() );
        payOrderPageItemRespVO.setChannelFeePrice( payOrderDO.getChannelFeePrice() );
        payOrderPageItemRespVO.setChannelFeeRate( payOrderDO.getChannelFeeRate() );
        payOrderPageItemRespVO.setChannelId( payOrderDO.getChannelId() );
        payOrderPageItemRespVO.setChannelOrderNo( payOrderDO.getChannelOrderNo() );
        payOrderPageItemRespVO.setChannelUserId( payOrderDO.getChannelUserId() );
        payOrderPageItemRespVO.setExpireTime( payOrderDO.getExpireTime() );
        payOrderPageItemRespVO.setExtensionId( payOrderDO.getExtensionId() );
        payOrderPageItemRespVO.setMerchantOrderId( payOrderDO.getMerchantOrderId() );
        payOrderPageItemRespVO.setNo( payOrderDO.getNo() );
        payOrderPageItemRespVO.setNotifyUrl( payOrderDO.getNotifyUrl() );
        if ( payOrderDO.getPrice() != null ) {
            payOrderPageItemRespVO.setPrice( payOrderDO.getPrice().longValue() );
        }
        if ( payOrderDO.getRefundPrice() != null ) {
            payOrderPageItemRespVO.setRefundPrice( payOrderDO.getRefundPrice().longValue() );
        }
        payOrderPageItemRespVO.setStatus( payOrderDO.getStatus() );
        payOrderPageItemRespVO.setSubject( payOrderDO.getSubject() );
        payOrderPageItemRespVO.setSuccessTime( payOrderDO.getSuccessTime() );
        payOrderPageItemRespVO.setUserIp( payOrderDO.getUserIp() );
        payOrderPageItemRespVO.setCreateTime( payOrderDO.getCreateTime() );
        payOrderPageItemRespVO.setId( payOrderDO.getId() );

        return payOrderPageItemRespVO;
    }

    protected List<PayOrderPageItemRespVO> payOrderDOListToPayOrderPageItemRespVOList(List<PayOrderDO> list) {
        if ( list == null ) {
            return null;
        }

        List<PayOrderPageItemRespVO> list1 = new ArrayList<PayOrderPageItemRespVO>( list.size() );
        for ( PayOrderDO payOrderDO : list ) {
            list1.add( payOrderDOToPayOrderPageItemRespVO( payOrderDO ) );
        }

        return list1;
    }
}
