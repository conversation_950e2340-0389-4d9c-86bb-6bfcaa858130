package cn.tianxing.cloud.module.pay.convert.transfer;

import cn.tianxing.cloud.framework.common.pojo.PageResult;
import cn.tianxing.cloud.framework.pay.core.client.dto.transfer.PayTransferUnifiedReqDTO;
import cn.tianxing.cloud.module.pay.api.transfer.dto.PayTransferCreateReqDTO;
import cn.tianxing.cloud.module.pay.controller.admin.demo.vo.transfer.PayDemoTransferCreateReqVO;
import cn.tianxing.cloud.module.pay.controller.admin.transfer.vo.PayTransferCreateReqVO;
import cn.tianxing.cloud.module.pay.controller.admin.transfer.vo.PayTransferPageItemRespVO;
import cn.tianxing.cloud.module.pay.controller.admin.transfer.vo.PayTransferRespVO;
import cn.tianxing.cloud.module.pay.dal.dataobject.transfer.PayTransferDO;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-18T16:56:03+0800",
    comments = "version: 1.6.3, compiler: Eclipse JDT (IDE) 3.42.50.v20250729-0351, environment: Java 21.0.8 (Eclipse Adoptium)"
)
public class PayTransferConvertImpl implements PayTransferConvert {

    @Override
    public PayTransferDO convert(PayTransferCreateReqDTO dto) {
        if ( dto == null ) {
            return null;
        }

        PayTransferDO payTransferDO = new PayTransferDO();

        payTransferDO.setAlipayLogonId( dto.getAlipayLogonId() );
        payTransferDO.setChannelCode( dto.getChannelCode() );
        Map<String, String> map = dto.getChannelExtras();
        if ( map != null ) {
            payTransferDO.setChannelExtras( new LinkedHashMap<String, String>( map ) );
        }
        payTransferDO.setMerchantTransferId( dto.getMerchantTransferId() );
        payTransferDO.setOpenid( dto.getOpenid() );
        payTransferDO.setPrice( dto.getPrice() );
        payTransferDO.setSubject( dto.getSubject() );
        payTransferDO.setType( dto.getType() );
        payTransferDO.setUserIp( dto.getUserIp() );
        payTransferDO.setUserName( dto.getUserName() );

        return payTransferDO;
    }

    @Override
    public PayTransferUnifiedReqDTO convert2(PayTransferDO dto) {
        if ( dto == null ) {
            return null;
        }

        PayTransferUnifiedReqDTO payTransferUnifiedReqDTO = new PayTransferUnifiedReqDTO();

        payTransferUnifiedReqDTO.setAlipayLogonId( dto.getAlipayLogonId() );
        Map<String, String> map = dto.getChannelExtras();
        if ( map != null ) {
            payTransferUnifiedReqDTO.setChannelExtras( new LinkedHashMap<String, String>( map ) );
        }
        payTransferUnifiedReqDTO.setNotifyUrl( dto.getNotifyUrl() );
        payTransferUnifiedReqDTO.setOpenid( dto.getOpenid() );
        payTransferUnifiedReqDTO.setPrice( dto.getPrice() );
        payTransferUnifiedReqDTO.setSubject( dto.getSubject() );
        payTransferUnifiedReqDTO.setType( dto.getType() );
        payTransferUnifiedReqDTO.setUserIp( dto.getUserIp() );
        payTransferUnifiedReqDTO.setUserName( dto.getUserName() );

        return payTransferUnifiedReqDTO;
    }

    @Override
    public PayTransferCreateReqDTO convert(PayTransferCreateReqVO vo) {
        if ( vo == null ) {
            return null;
        }

        PayTransferCreateReqDTO payTransferCreateReqDTO = new PayTransferCreateReqDTO();

        payTransferCreateReqDTO.setAlipayLogonId( vo.getAlipayLogonId() );
        payTransferCreateReqDTO.setChannelCode( vo.getChannelCode() );
        Map<String, String> map = vo.getChannelExtras();
        if ( map != null ) {
            payTransferCreateReqDTO.setChannelExtras( new LinkedHashMap<String, String>( map ) );
        }
        payTransferCreateReqDTO.setMerchantTransferId( vo.getMerchantTransferId() );
        payTransferCreateReqDTO.setOpenid( vo.getOpenid() );
        payTransferCreateReqDTO.setPrice( vo.getPrice() );
        payTransferCreateReqDTO.setSubject( vo.getSubject() );
        payTransferCreateReqDTO.setType( vo.getType() );
        payTransferCreateReqDTO.setUserName( vo.getUserName() );

        return payTransferCreateReqDTO;
    }

    @Override
    public PayTransferCreateReqDTO convert(PayDemoTransferCreateReqVO vo) {
        if ( vo == null ) {
            return null;
        }

        PayTransferCreateReqDTO payTransferCreateReqDTO = new PayTransferCreateReqDTO();

        payTransferCreateReqDTO.setAlipayLogonId( vo.getAlipayLogonId() );
        payTransferCreateReqDTO.setOpenid( vo.getOpenid() );
        payTransferCreateReqDTO.setPrice( vo.getPrice() );
        payTransferCreateReqDTO.setType( vo.getType() );
        payTransferCreateReqDTO.setUserName( vo.getUserName() );

        return payTransferCreateReqDTO;
    }

    @Override
    public PayTransferRespVO convert(PayTransferDO bean) {
        if ( bean == null ) {
            return null;
        }

        PayTransferRespVO payTransferRespVO = new PayTransferRespVO();

        payTransferRespVO.setAlipayLogonId( bean.getAlipayLogonId() );
        payTransferRespVO.setAppId( bean.getAppId() );
        payTransferRespVO.setChannelCode( bean.getChannelCode() );
        payTransferRespVO.setChannelErrorCode( bean.getChannelErrorCode() );
        payTransferRespVO.setChannelErrorMsg( bean.getChannelErrorMsg() );
        Map<String, String> map = bean.getChannelExtras();
        if ( map != null ) {
            payTransferRespVO.setChannelExtras( new LinkedHashMap<String, String>( map ) );
        }
        payTransferRespVO.setChannelId( bean.getChannelId() );
        payTransferRespVO.setChannelNotifyData( bean.getChannelNotifyData() );
        payTransferRespVO.setChannelTransferNo( bean.getChannelTransferNo() );
        payTransferRespVO.setCreateTime( bean.getCreateTime() );
        payTransferRespVO.setId( bean.getId() );
        payTransferRespVO.setMerchantTransferId( bean.getMerchantTransferId() );
        payTransferRespVO.setNo( bean.getNo() );
        payTransferRespVO.setNotifyUrl( bean.getNotifyUrl() );
        payTransferRespVO.setOpenid( bean.getOpenid() );
        payTransferRespVO.setPrice( bean.getPrice() );
        payTransferRespVO.setStatus( bean.getStatus() );
        payTransferRespVO.setSubject( bean.getSubject() );
        payTransferRespVO.setSuccessTime( bean.getSuccessTime() );
        payTransferRespVO.setType( bean.getType() );
        payTransferRespVO.setUserIp( bean.getUserIp() );
        payTransferRespVO.setUserName( bean.getUserName() );

        return payTransferRespVO;
    }

    @Override
    public PageResult<PayTransferPageItemRespVO> convertPage(PageResult<PayTransferDO> pageResult) {
        if ( pageResult == null ) {
            return null;
        }

        PageResult<PayTransferPageItemRespVO> pageResult1 = new PageResult<PayTransferPageItemRespVO>();

        pageResult1.setList( payTransferDOListToPayTransferPageItemRespVOList( pageResult.getList() ) );
        pageResult1.setTotal( pageResult.getTotal() );

        return pageResult1;
    }

    protected PayTransferPageItemRespVO payTransferDOToPayTransferPageItemRespVO(PayTransferDO payTransferDO) {
        if ( payTransferDO == null ) {
            return null;
        }

        PayTransferPageItemRespVO payTransferPageItemRespVO = new PayTransferPageItemRespVO();

        payTransferPageItemRespVO.setAlipayLogonId( payTransferDO.getAlipayLogonId() );
        payTransferPageItemRespVO.setAppId( payTransferDO.getAppId() );
        payTransferPageItemRespVO.setChannelCode( payTransferDO.getChannelCode() );
        payTransferPageItemRespVO.setChannelId( payTransferDO.getChannelId() );
        payTransferPageItemRespVO.setChannelTransferNo( payTransferDO.getChannelTransferNo() );
        payTransferPageItemRespVO.setCreateTime( payTransferDO.getCreateTime() );
        payTransferPageItemRespVO.setId( payTransferDO.getId() );
        payTransferPageItemRespVO.setMerchantTransferId( payTransferDO.getMerchantTransferId() );
        payTransferPageItemRespVO.setNo( payTransferDO.getNo() );
        payTransferPageItemRespVO.setOpenid( payTransferDO.getOpenid() );
        payTransferPageItemRespVO.setPrice( payTransferDO.getPrice() );
        payTransferPageItemRespVO.setStatus( payTransferDO.getStatus() );
        payTransferPageItemRespVO.setSubject( payTransferDO.getSubject() );
        payTransferPageItemRespVO.setSuccessTime( payTransferDO.getSuccessTime() );
        payTransferPageItemRespVO.setType( payTransferDO.getType() );
        payTransferPageItemRespVO.setUserName( payTransferDO.getUserName() );

        return payTransferPageItemRespVO;
    }

    protected List<PayTransferPageItemRespVO> payTransferDOListToPayTransferPageItemRespVOList(List<PayTransferDO> list) {
        if ( list == null ) {
            return null;
        }

        List<PayTransferPageItemRespVO> list1 = new ArrayList<PayTransferPageItemRespVO>( list.size() );
        for ( PayTransferDO payTransferDO : list ) {
            list1.add( payTransferDOToPayTransferPageItemRespVO( payTransferDO ) );
        }

        return list1;
    }
}
