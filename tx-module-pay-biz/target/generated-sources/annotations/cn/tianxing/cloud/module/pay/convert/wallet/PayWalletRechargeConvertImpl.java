package cn.tianxing.cloud.module.pay.convert.wallet;

import cn.tianxing.cloud.module.pay.controller.app.wallet.vo.recharge.AppPayWalletRechargeCreateRespVO;
import cn.tianxing.cloud.module.pay.dal.dataobject.wallet.PayWalletRechargeDO;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-18T16:56:03+0800",
    comments = "version: 1.6.3, compiler: Eclipse JDT (IDE) 3.42.50.v20250729-0351, environment: Java 21.0.8 (Eclipse Adoptium)"
)
public class PayWalletRechargeConvertImpl implements PayWalletRechargeConvert {

    @Override
    public PayWalletRechargeDO convert(Long walletId, Integer payPrice, Integer bonusPrice, Long packageId) {
        if ( walletId == null && payPrice == null && bonusPrice == null && packageId == null ) {
            return null;
        }

        PayWalletRechargeDO payWalletRechargeDO = new PayWalletRechargeDO();

        payWalletRechargeDO.setWalletId( walletId );
        payWalletRechargeDO.setPayPrice( payPrice );
        payWalletRechargeDO.setBonusPrice( bonusPrice );
        payWalletRechargeDO.setPackageId( packageId );
        payWalletRechargeDO.setTotalPrice( payPrice + bonusPrice );

        return payWalletRechargeDO;
    }

    @Override
    public AppPayWalletRechargeCreateRespVO convert(PayWalletRechargeDO bean) {
        if ( bean == null ) {
            return null;
        }

        AppPayWalletRechargeCreateRespVO appPayWalletRechargeCreateRespVO = new AppPayWalletRechargeCreateRespVO();

        appPayWalletRechargeCreateRespVO.setId( bean.getId() );
        appPayWalletRechargeCreateRespVO.setPayOrderId( bean.getPayOrderId() );

        return appPayWalletRechargeCreateRespVO;
    }
}
